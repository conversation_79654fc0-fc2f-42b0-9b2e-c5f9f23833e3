import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import rateLimit from 'express-rate-limit';

import config from './config/index.js';
import logger from './utils/logger.js';
import { TokenDiscoveryService } from './services/TokenDiscoveryService.js';
import { PriceFeedService } from './services/PriceFeedService.js';
import { OpportunityDetectionService } from './services/OpportunityDetectionService.js';
import { ExecutionService } from './services/ExecutionService.js';
import { RiskManagementService } from './services/RiskManagementService.js';
import { AnalyticsService } from './services/AnalyticsService.js';
import { WebSocketManager } from './websocket/WebSocketManager.js';

// Import routes
import tokenRoutes from './routes/tokens.js';
import opportunityRoutes from './routes/opportunities.js';
import tradeRoutes from './routes/trades.js';
import systemRoutes from './routes/system.js';
import analyticsRoutes from './routes/analytics.js';

class MEVArbitrageBot {
  private app: express.Application;
  private server: any;
  private wss: WebSocketServer;
  private wsManager: WebSocketManager;
  
  // Services
  private tokenDiscoveryService: TokenDiscoveryService;
  private priceFeedService: PriceFeedService;
  private opportunityDetectionService: OpportunityDetectionService;
  private executionService: ExecutionService;
  private riskManagementService: RiskManagementService;
  private analyticsService: AnalyticsService;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.wss = new WebSocketServer({ server: this.server });
    this.wsManager = new WebSocketManager(this.wss);
    
    this.initializeServices();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupWebSocket();
  }

  private initializeServices() {
    logger.info('Initializing services...');
    
    this.tokenDiscoveryService = new TokenDiscoveryService();
    this.priceFeedService = new PriceFeedService();
    this.opportunityDetectionService = new OpportunityDetectionService(
      this.priceFeedService,
      this.tokenDiscoveryService
    );
    this.executionService = new ExecutionService();
    this.riskManagementService = new RiskManagementService();
    this.analyticsService = new AnalyticsService();
    
    // Set up service dependencies
    this.opportunityDetectionService.on('opportunity', (opportunity) => {
      this.wsManager.broadcast('opportunity', opportunity);
      this.executionService.evaluateOpportunity(opportunity);
    });
    
    this.executionService.on('trade', (trade) => {
      this.wsManager.broadcast('trade', trade);
      this.analyticsService.recordTrade(trade);
    });
    
    this.riskManagementService.on('emergencyStop', () => {
      this.wsManager.broadcast('emergencyStop', { active: true });
      this.executionService.pause();
    });
    
    logger.info('Services initialized successfully');
  }

  private setupMiddleware() {
    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP, please try again later.'
    });

    this.app.use(limiter);
    this.app.use(cors());
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // Logging middleware
    this.app.use((req, res, next) => {
      logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      next();
    });
  }

  private setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        services: {
          tokenDiscovery: this.tokenDiscoveryService.isHealthy(),
          priceFeed: this.priceFeedService.isHealthy(),
          opportunityDetection: this.opportunityDetectionService.isHealthy(),
          execution: this.executionService.isHealthy(),
          riskManagement: this.riskManagementService.isHealthy(),
          analytics: this.analyticsService.isHealthy()
        }
      });
    });

    // API routes
    this.app.use('/api/tokens', tokenRoutes(this.tokenDiscoveryService));
    this.app.use('/api/opportunities', opportunityRoutes(this.opportunityDetectionService));
    this.app.use('/api/trades', tradeRoutes(this.executionService));
    this.app.use('/api/system', systemRoutes(this.riskManagementService));
    this.app.use('/api/analytics', analyticsRoutes(this.analyticsService));

    // Error handling
    this.app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
      logger.error('Unhandled error:', err);
      res.status(500).json({
        error: 'Internal server error',
        message: config.NODE_ENV === 'development' ? err.message : 'Something went wrong'
      });
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({ error: 'Route not found' });
    });
  }

  private setupWebSocket() {
    this.wsManager.on('connection', (ws, request) => {
      logger.info('New WebSocket connection established');
      
      // Send initial data
      ws.send(JSON.stringify({
        type: 'welcome',
        data: {
          message: 'Connected to MEV Arbitrage Bot',
          timestamp: new Date().toISOString()
        }
      }));
    });

    this.wsManager.on('message', (ws, message) => {
      try {
        const data = JSON.parse(message.toString());
        this.handleWebSocketMessage(ws, data);
      } catch (error) {
        logger.error('Invalid WebSocket message:', error);
        ws.send(JSON.stringify({
          type: 'error',
          data: { message: 'Invalid message format' }
        }));
      }
    });
  }

  private handleWebSocketMessage(ws: any, data: any) {
    switch (data.type) {
      case 'subscribe':
        this.wsManager.subscribe(ws, data.channel);
        break;
      case 'unsubscribe':
        this.wsManager.unsubscribe(ws, data.channel);
        break;
      case 'ping':
        ws.send(JSON.stringify({ type: 'pong', timestamp: Date.now() }));
        break;
      default:
        logger.warn('Unknown WebSocket message type:', data.type);
    }
  }

  public async start() {
    try {
      // Start services
      await this.tokenDiscoveryService.start();
      await this.priceFeedService.start();
      await this.opportunityDetectionService.start();
      await this.executionService.start();
      await this.riskManagementService.start();
      await this.analyticsService.start();

      // Start server
      const port = parseInt(config.PORT);
      this.server.listen(port, () => {
        logger.info(`MEV Arbitrage Bot server started on port ${port}`);
        logger.info(`Environment: ${config.NODE_ENV}`);
        logger.info(`WebSocket server ready for connections`);
      });

      // Graceful shutdown
      process.on('SIGTERM', () => this.shutdown());
      process.on('SIGINT', () => this.shutdown());

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  private async shutdown() {
    logger.info('Shutting down MEV Arbitrage Bot...');
    
    try {
      // Stop services
      await this.tokenDiscoveryService.stop();
      await this.priceFeedService.stop();
      await this.opportunityDetectionService.stop();
      await this.executionService.stop();
      await this.riskManagementService.stop();
      await this.analyticsService.stop();

      // Close WebSocket server
      this.wss.close();

      // Close HTTP server
      this.server.close(() => {
        logger.info('Server shutdown complete');
        process.exit(0);
      });
    } catch (error) {
      logger.error('Error during shutdown:', error);
      process.exit(1);
    }
  }
}

// Start the bot
const bot = new MEVArbitrageBot();
bot.start().catch((error) => {
  logger.error('Failed to start MEV Arbitrage Bot:', error);
  process.exit(1);
});
