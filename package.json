{"name": "mev-arbitrage-bot", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "vite", "dev:backend": "nodemon --exec tsx backend/server.ts", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "vite build", "build:backend": "tsc -p backend/tsconfig.json", "preview": "vite preview", "start": "node dist/backend/server.js", "test": "jest", "test:contracts": "hardhat test", "compile:contracts": "hardhat compile", "deploy:contracts": "hardhat run scripts/deploy.ts"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "@google/genai": "^1.1.0", "recharts": "^2.15.3", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "cors": "^2.8.5", "ws": "^8.14.2", "redis": "^4.6.10", "ethers": "^6.8.1", "web3": "^4.2.2", "axios": "^1.6.2", "lodash": "^4.17.21", "uuid": "^9.0.1", "zod": "^3.22.4", "decimal.js": "^10.4.3", "node-cron": "^3.0.3", "winston": "^3.11.0"}, "devDependencies": {"@types/node": "^22.14.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/ws": "^8.5.10", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@types/node-cron": "^3.0.11", "typescript": "~5.7.2", "vite": "^6.2.0", "tsx": "^4.6.2", "nodemon": "^3.0.2", "concurrently": "^8.2.2", "jest": "^29.7.0", "@types/jest": "^29.5.8", "hardhat": "^2.19.1", "@nomicfoundation/hardhat-toolbox": "^4.0.0", "@openzeppelin/contracts": "^5.0.0"}}